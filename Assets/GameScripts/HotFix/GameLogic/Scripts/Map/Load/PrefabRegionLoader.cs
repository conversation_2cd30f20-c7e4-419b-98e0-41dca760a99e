using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using System.Threading;
using System.Linq;

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    private Dictionary<Vector3, PrefabData> _prefabMap = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 已加载的位置集合
    private HashSet<Vector3> _loadedPositions = new();

    // 当前加载在场景中的实例列表
    private List<LoadedItem> _loadedInstances = new();

    // 缓存的附近格子中的所有 prefab 列表（避免重复查找）
    private List<PrefabItem> _cachedNearbyPrefabs = new();

    // GPU实例化相关数据结构
    private Dictionary<string, PrefabRenderData> _prefabRenderCache = new();
    private Dictionary<string, List<Matrix4x4>> _visibleInstances = new();
    private Dictionary<string, List<Vector3>> _visiblePositions = new();
    private Camera _mainCamera;
    private Vector3? _lastGPUUpdatePosition = null;

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    private string _regionName;
    private Vector3? _lastCenter = null;

    // 加载半径，单位是世界坐标（米）
    public float loadRadius = 70f;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 卸载半径，设为加载半径的 1.5 倍
    private float unloadRadius => loadRadius * 1.5f;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, PrefabTransmitMapInfo> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    private CancellationTokenSource _loadCancellationTokenSource;
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        _mainCamera = Camera.main;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// 现在包含GPU实例化渲染
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition, 5).Forget();
        // 强制更新GPU实例化可见性
        ForceUpdateGPUInstancing(worldPosition);
    }

    /// <summary>
    /// 每帧更新方法，需要在外部的Update中调用来保持GPU实例化渲染
    /// </summary>
    public void Update(Vector3 worldPosition)
    {
        // 更新GPU实例化可见性（只在位置变化时重新计算）
        UpdateGPUInstancing(worldPosition);
        // 每帧渲染GPU实例
        RenderGPUInstances();
    }

    /// <summary>
    /// 强制更新GPU实例化，忽略位置变化检查
    /// </summary>
    public void ForceUpdateGPUInstancing(Vector3 worldPosition)
    {
        if (_mainCamera == null) _mainCamera = Camera.main;
        if (_mainCamera == null) return;

        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);
        UpdateVisibleInstances(localPosition);
        _lastGPUUpdatePosition = localPosition;
    }

    /// <summary>
    /// 析构方法，确保资源正确释放
    /// </summary>
    ~PrefabRegionLoader()
    {
        ClearGPUCache();
    }

    /// <summary>
    /// GPU实例化渲染更新方法，需要在Update中调用
    /// </summary>
    public void UpdateGPUInstancing(Vector3 worldPosition)
    {
        if (_mainCamera == null) _mainCamera = Camera.main;
        if (_mainCamera == null) return;

        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 只在位置变化超过一定距离时才重新计算可见实例
        if (!_lastGPUUpdatePosition.HasValue ||
            Vector3.Distance(localPosition, _lastGPUUpdatePosition.Value) > 5f)
        {
            UpdateVisibleInstances(localPosition);
            _lastGPUUpdatePosition = localPosition;
        }
    }

    /// <summary>
    /// 在LateUpdate中调用，确保在所有Update之后渲染
    /// 这个方法需要每帧调用来保持GPU实例化渲染
    /// </summary>
    public void RenderGPUInstances()
    {
        RenderInstances();
    }

    /// <summary>
    /// 获取当前GPU实例化统计信息
    /// </summary>
    public string GetGPUInstanceStats()
    {
        int totalInstances = 0;
        int totalPrefabTypes = 0;

        foreach (var kvp in _visibleInstances)
        {
            if (kvp.Value.Count > 0)
            {
                totalInstances += kvp.Value.Count;
                totalPrefabTypes++;
            }
        }

        return $"GPU实例化: {totalInstances} 个实例, {totalPrefabTypes} 种Prefab, {_prefabRenderCache.Count} 个缓存";
    }

    /// <summary>
    /// 异步缓存Prefab的渲染数据
    /// </summary>
    private async UniTask CachePrefabRenderData(string prefabName)
    {
        if (_prefabRenderCache.ContainsKey(prefabName)) return;

        var renderData = new PrefabRenderData { isLoading = true };
        _prefabRenderCache[prefabName] = renderData;

        try
        {
            GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(prefabName);
            if (prefab != null)
            {
                ExtractMeshData(prefab, renderData);
                renderData.isLoaded = true;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to cache prefab render data for {prefabName}: {e.Message}");
        }
        finally
        {
            renderData.isLoading = false;
        }
    }

    /// <summary>
    /// 从Prefab中提取Mesh数据
    /// </summary>
    private void ExtractMeshData(GameObject prefab, PrefabRenderData renderData)
    {
        var renderers = prefab.GetComponentsInChildren<MeshRenderer>();
        foreach (var renderer in renderers)
        {
            var meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                // var sharedMaterials = new List<Material>();
                foreach (var mat in renderer.sharedMaterials)
                {
                    if (mat != null && !mat.enableInstancing)
                    {
                        mat.enableInstancing = true;
                    }
                }
                var meshInfo = new MeshRenderInfo
                {
                    mesh = meshFilter.sharedMesh,
                    materials = renderer.sharedMaterials,
                    localToWorld = renderer.transform.localToWorldMatrix,
                    subMeshCount = meshFilter.sharedMesh.subMeshCount
                };
                renderData.meshInfos.Add(meshInfo);
            }
        }
    }

    // private void EnableInstancing(GameObject obj)
    // {
    //     var renderers = obj.GetComponentsInChildren<MeshRenderer>(true);
    //     foreach (var renderer in renderers)
    //     {
    //         foreach (var mat in renderer.sharedMaterials)
    //         {
    //             if (mat != null && !mat.enableInstancing)
    //             {
    //                 mat.enableInstancing = true;
    //             }
    //         }
    //     }
    // }

    /// <summary>
    /// 更新可见实例列表
    /// </summary>
    private void UpdateVisibleInstances(Vector3 localPosition)
    {
        // 清空之前的可见实例
        foreach (var kvp in _visibleInstances)
        {
            kvp.Value.Clear();
        }
        foreach (var kvp in _visiblePositions)
        {
            kvp.Value.Clear();
        }

        // 遍历附近的prefab，更新可见实例
        foreach (var item in _cachedNearbyPrefabs)
        {
            float dist = Vector3.Distance(localPosition, item.position);
            if (dist <= loadRadius)
            {
                // 跳过传送点，它们仍使用传统方式渲染
                if (!string.IsNullOrEmpty(item.transmitName)) continue;

                // 只处理地图prefab
                if (!item.prefabName.StartsWith("HeartGold_map")) continue;

                // 只渲染已经标记为加载的位置（避免闪烁）
                if (!_loadedPositions.Contains(item.position)) continue;

                // 确保prefab数据已缓存
                if (!_prefabRenderCache.ContainsKey(item.prefabName))
                {
                    CachePrefabRenderData(item.prefabName).Forget();
                    continue;
                }

                var renderData = _prefabRenderCache[item.prefabName];
                if (!renderData.isLoaded) continue;

                // 添加到可见实例列表
                if (!_visibleInstances.ContainsKey(item.prefabName))
                {
                    _visibleInstances[item.prefabName] = new List<Matrix4x4>();
                    _visiblePositions[item.prefabName] = new List<Vector3>();
                }

                // 计算变换矩阵
                var matrix = Matrix4x4.TRS(
                    _mapContainer.TransformPoint(item.position),
                    Quaternion.Euler(item.rotation),
                    item.scale
                );

                _visibleInstances[item.prefabName].Add(matrix);
                _visiblePositions[item.prefabName].Add(item.position);
            }
        }

        // 限制缓存大小，防止内存爆炸
        LimitGPUCacheSize();
    }

    /// <summary>
    /// 使用GPU实例化渲染所有可见实例
    /// </summary>
    private void RenderInstances()
    {
        if (_mainCamera == null) return;

        int totalInstances = 0;
        int totalDrawCalls = 0;

        foreach (var kvp in _visibleInstances)
        {
            string prefabName = kvp.Key;
            var matrices = kvp.Value;

            if (matrices.Count == 0) continue;

            var renderData = _prefabRenderCache[prefabName];
            if (!renderData.isLoaded) continue;

            totalInstances += matrices.Count;

            // 分批渲染，Unity的Graphics.DrawMeshInstanced最多支持1023个实例
            const int maxInstancesPerBatch = 1023;

            for (int batchStart = 0; batchStart < matrices.Count; batchStart += maxInstancesPerBatch)
            {
                int batchSize = Mathf.Min(maxInstancesPerBatch, matrices.Count - batchStart);
                var batchMatrices = matrices.GetRange(batchStart, batchSize).ToArray();

                // 渲染每个mesh
                foreach (var meshInfo in renderData.meshInfos)
                {
                    if (meshInfo.mesh == null || meshInfo.materials == null) continue;

                    // 为每个子网格渲染
                    for (int subMeshIndex = 0; subMeshIndex < meshInfo.subMeshCount && subMeshIndex < meshInfo.materials.Length; subMeshIndex++)
                    {
                        var material = meshInfo.materials[subMeshIndex];
                        if (material == null || !material.enableInstancing) continue;

                        try
                        {
                            // 使用GPU实例化渲染
                            Graphics.DrawMeshInstanced(
                                meshInfo.mesh,
                                subMeshIndex,
                                material,
                                batchMatrices,
                                batchSize,
                                null,
                                UnityEngine.Rendering.ShadowCastingMode.On,
                                true,
                                0,
                                _mainCamera
                            );

                            totalDrawCalls++;
                        }
                        catch (System.Exception e)
                        {
                            if (debugLog)
                                Debug.LogWarning($"GPU实例化渲染失败: {prefabName}, 材质: {material.name}, 错误: {e.Message}");
                        }
                    }
                }
            }
        }

        // 调试信息
        if (debugLog && totalInstances > 0)
        {
            Debug.Log($"🎮 GPU实例化渲染: {totalInstances} 个实例, {totalDrawCalls} 个DrawCall, {_prefabRenderCache.Count} 个缓存Prefab");
        }
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    public async UniTask AsyncLoadNearby(Vector3 worldPosition, int frame = 0)
    {
        if (_isloading) return;
        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;
        _loadCancellationTokenSource = new CancellationTokenSource();
        var token = _loadCancellationTokenSource.Token;

        // 转换为 local 坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 如果第一次加载，或距离上次加载中心超出范围，就重新构建 nearby 缓存
        if (!_lastCenter.HasValue || Vector3.Distance(localPosition, _lastCenter.Value) > loadRadius)
        {
            _lastCenter = localPosition;
            _cachedNearbyPrefabs.Clear();

            int searchRadius = Mathf.CeilToInt((loadRadius * 2) / _cellSize);

            // Y 方向的搜索限制层数：只搜索上下共3层（-1, 0, +1）
            int ySearchLimit = 1;

            Vector3Int centerCell = WorldToCell(localPosition);

            for (int dx = -searchRadius; dx <= searchRadius; dx++)
            {
                for (int dy = -ySearchLimit; dy <= ySearchLimit; dy++)
                {
                    for (int dz = -searchRadius; dz <= searchRadius; dz++)
                    {
                        Vector3Int cell = new Vector3Int(centerCell.x + dx, centerCell.y + dy, centerCell.z + dz);

                        if (_spatialHashMap.TryGetValue(cell, out var list))
                            _cachedNearbyPrefabs.AddRange(list);
                    }
                }
            }
        }
        
        var cachedNearbyCopy = new List<PrefabItem>(_cachedNearbyPrefabs);

        foreach (var item in cachedNearbyCopy)
        {
            float dist = Vector3.Distance(localPosition, item.position);

            if (dist <= loadRadius && !_loadedPositions.Contains(item.position))
            {
                // 如果是传送点
                if (!string.IsNullOrEmpty(item.transmitName) && _transmitPoints.TryGetValue(item.transmitName, out var transmitInfo))
                {
                    GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(item.prefabName);
                    if (prefab != null)
                    {
                        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
                        GameObject transmitObj = objs[0];//GameObject.Instantiate(prefab, _mapContainer);
                        transmitObj.transform.SetParent(_mapContainer);
                        transmitObj.transform.localPosition = transmitInfo.position;
                        transmitObj.transform.localRotation = Quaternion.identity;

                        var transmitComponent = transmitObj.GetComponent<TransmitMapComponent>();
                        transmitComponent.Name = transmitInfo.name;
                        transmitComponent.ToMapName = transmitInfo.toMapName;
                        transmitComponent.prefabRegionLoader = this;
                        transmitComponent.transform.localScale = transmitInfo.localScale;
                        // transmitComponent.isHidenEffect = transmitInfo.isHidenEffect;
                        transmitComponent.isPointCenter = transmitInfo.isPokeCenter;
                        transmitComponent.SetIsHidenEffect(transmitInfo.isHidenEffect);

                        var collider = transmitObj.GetComponent<BoxCollider>();
                        collider.size = transmitInfo.boxColliderSize;
                        if(transmitInfo.isHidenEffect) {
                            collider.size = new Vector3(0.5f, 0.5f, 0.5f);
                        }
                        collider.isTrigger = true;

                        _loadedTransmitPoints[transmitInfo.name] = transmitObj;
                        _loadedPositions.Add(item.position);
                        _loadedInstances.Add(new LoadedItem { instance = transmitObj, position = item.position });

                        if (debugLog)
                            Debug.Log($"✅ Created transmit point: {transmitInfo.name} at {transmitInfo.position}");
                    }
                }
                else // 普通 prefab - 现在使用GPU实例化，不创建GameObject
                {
                    if(!item.prefabName.StartsWith("HeartGold_map")) {
                        continue;
                    }

                    // 只需要确保prefab数据已缓存，不创建实际的GameObject
                    if (!_prefabRenderCache.ContainsKey(item.prefabName))
                    {
                        CachePrefabRenderData(item.prefabName).Forget();
                    }

                    // 标记位置为已加载（用于GPU实例化）
                    _loadedPositions.Add(item.position);

                    if (debugLog) {
                        Debug.Log($"✅ Cached for GPU rendering: {item.prefabName} at {item.position}");
                    }

                    if(frame > 0) {
                        await UniTask.DelayFrame(frame, PlayerLoopTiming.Update, token);
                    }
                }
            }
        }

        UnloadFar(localPosition);
        _isloading = false;
    }
    public void CancelLoading()
    {
        if (_isloading)
        {
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _isloading = false;
        }
    }

    /// <summary>
    /// 清理GPU实例化缓存，防止内存泄漏
    /// </summary>
    public void ClearGPUCache()
    {
        _prefabRenderCache.Clear();
        foreach (var kvp in _visibleInstances)
        {
            kvp.Value.Clear();
        }
        foreach (var kvp in _visiblePositions)
        {
            kvp.Value.Clear();
        }
        _visibleInstances.Clear();
        _visiblePositions.Clear();
    }

    /// <summary>
    /// 限制GPU缓存大小，防止内存爆炸
    /// </summary>
    private void LimitGPUCacheSize()
    {
        const int maxCachedPrefabs = 500; // 最多缓存500个不同的prefab

        if (_prefabRenderCache.Count > maxCachedPrefabs)
        {
            // 移除最少使用的prefab缓存
            var sortedCache = _prefabRenderCache.OrderBy(kvp =>
                _visibleInstances.ContainsKey(kvp.Key) ? _visibleInstances[kvp.Key].Count : 0
            ).ToList();

            int removeCount = _prefabRenderCache.Count - maxCachedPrefabs;
            for (int i = 0; i < removeCount; i++)
            {
                string prefabName = sortedCache[i].Key;
                _prefabRenderCache.Remove(prefabName);
                if (_visibleInstances.ContainsKey(prefabName))
                {
                    _visibleInstances[prefabName].Clear();
                    _visiblePositions[prefabName].Clear();
                }
            }
        }
    }
    /// <summary>
    /// 卸载离当前位置太远的 prefab 实例
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        for (int i = _loadedInstances.Count - 1; i >= 0; i--)
        {
            var item = _loadedInstances[i];
            if (Vector3.Distance(localPos, item.position) > unloadRadius)
            {
                if (_loadedTransmitPoints.ContainsValue(item.instance))
                {
                    var transmitComponent = item.instance.GetComponent<TransmitMapComponent>();
                    if (transmitComponent != null)
                    {
                        _loadedTransmitPoints.Remove(transmitComponent.Name);
                    }
                }

                GameObject.Destroy(item.instance);
                _loadedPositions.Remove(item.position);
                _loadedInstances.RemoveAt(i);

                if (debugLog)
                    Debug.Log($"♻️ Unloaded object at {item.position}");
            }
        }
    }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _prefabMap.Clear();
        _transmitPoints.Clear();
        List<PrefabData> prefabData = new();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.mainMap != null) {
            prefabData.AddRange(mapData.mainMap);
        }
        if(mapData.buildingMap != null) {
            prefabData.AddRange(mapData.buildingMap);
        }
        if(mapData.transferMap != null) {
            prefabData.AddRange(mapData.transferMap);
        }
        if(mapData.envMap != null) {
            prefabData.AddRange(mapData.envMap);
        }
        if(mapData.envOutBuildingMap != null) {
            prefabData.AddRange(mapData.envOutBuildingMap);
        }
        if(mapData.multipleOutMap != null) {
            prefabData.AddRange(mapData.multipleOutMap);
        }
        if(mapData.interiorMap != null) {
            prefabData.AddRange(mapData.interiorMap);
        }
        if(mapData.otherMap != null) {
            prefabData.AddRange(mapData.otherMap);
        }
        foreach (var item in prefabData)
        {
            Vector3 pos = item.position;
            _prefabMap[pos] = item;

            if (item.transmitMapInfo != null && !string.IsNullOrEmpty(item.transmitMapInfo.name))
            {
                _transmitPoints[item.transmitMapInfo.name] = item.transmitMapInfo;
            }
        }
        foreach (var initPostion in mapData.initPostions)
        {
            _transmitPoints[initPostion.transmitMapInfo.name] = initPostion.transmitMapInfo;
        }
        // foreach (var configFileName in _configFileNames)
        // {
        //     var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(configFileName).text;
        //     var list = JsonUtility.FromJson<PrefabDataList>(json);

        //     foreach (var item in list.data)
        //     {
        //         Vector3 pos = item.position;
        //         _prefabMap[pos] = item;

        //         if (item.transmitMapInfo != null && !string.IsNullOrEmpty(item.transmitMapInfo.name))
        //         {
        //             _transmitPoints[item.transmitMapInfo.name] = item.transmitMapInfo;
        //         }
        //     }
        // }

        BuildSpatialHash();
    }

    /// <summary>
    /// 构建三维空间哈希映射
    /// </summary>
    private void BuildSpatialHash()
    {
        _spatialHashMap.Clear();

        foreach (var kv in _prefabMap)
        {
            Vector3 pos = kv.Key;
            Vector3Int cell = WorldToCell(pos);

            if (!_spatialHashMap.TryGetValue(cell, out var list))
            {
                list = new List<PrefabItem>();
                _spatialHashMap[cell] = list;
            }

            list.Add(new PrefabItem
            {
                position = pos,
                rotation = kv.Value.rotation,
                scale = kv.Value.scale,
                prefabName = kv.Value.prefab,
                displayName = kv.Value.name,
                transmitName = kv.Value.transmitMapInfo?.name ?? "",
                mapTileInfos = kv.Value.mapTileInfos
            });
        }
    }

    /// <summary>
    /// 将世界坐标转为空间格子坐标（三维）
    /// </summary>
    private Vector3Int WorldToCell(Vector3 pos)
    {
        return new Vector3Int(
            Mathf.FloorToInt(pos.x / _cellSize),
            Mathf.FloorToInt(pos.y / _cellSize),
            Mathf.FloorToInt(pos.z / _cellSize)
        );
    }

    public PrefabTransmitMapInfo GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    private class PrefabItem
    {
        public Vector3 position;
        public Vector3 rotation;
        public Vector3 scale;
        public string prefabName;
        public string displayName;
        public string transmitName;
        public Dictionary<string, PrefabMapTileInfo> mapTileInfos;
    }
    private class LoadedItem
    {
        public GameObject instance;
        public Vector3 position;
    }

    /// <summary>
    /// GPU实例化渲染数据
    /// </summary>
    private class PrefabRenderData
    {
        public List<MeshRenderInfo> meshInfos = new();
        public bool isLoaded = false;
        public bool isLoading = false;
    }

    /// <summary>
    /// 单个Mesh的渲染信息
    /// </summary>
    private class MeshRenderInfo
    {
        public Mesh mesh;
        public Material[] materials;
        public Matrix4x4 localToWorld;
        public int subMeshCount;
    }
}
