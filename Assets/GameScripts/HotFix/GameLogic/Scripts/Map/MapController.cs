using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using MoreMountains.TopDownEngine;
using PetsServices;
using PetsServices.Net;
using PimDeWitte.UnityMainThreadDispatcher;
using UnityEngine;
using Unity.Cinemachine;
using GameLogic.Scripts.Extension;

public class MapController : MonoBehaviour
{
    static public MapController Current { get; private set; }
    public WindowLightManager lightManager;
    public LevelManager levelManager;
    public MapMenuUI mapMenuUI { get; private set; }
    public WeatherMgr weatherMgr;
    public MapCharacterMgr mapCharacterMgr;
    public NinTransferMapMgr transferMapMgr;
    public Yarn.Unity.DialogueRunner dialogueRunner;
    public BattleController battleController;
    public CinemachineCamera cinemachineCamera;
    public MapLoader mapLoader;
    public NinYarnMapMgr yarnMapMgr;
    public NinYarnMgr yarnMgr;
    public Int64 lastLocTs;
    private Int64 _lastUpdateMapTs;
    // private Vector3 _lastUpPosition;
    // private EnterBattleOption _lastBattleOption;
    private double _createTs;
    // private bool isload;
    public bool IsTestLocal = false;

    NinPlotChatacter _testPlotChatacter;
    void Awake()
    {
        MapController.Current = this;
        _ = NinSysNotificationMgr.share;
        GameContext.IsTestLocal = IsTestLocal;
        if(IsTestLocal) {
            TestPrepareEnter().Forget();
        }
        weatherMgr.ActiveCozyWeather();
        // weatherMgr = new WeatherMgr(transform);
        SoundPlayer.InitMusicPlayer();
        cinemachineCamera?.UpdateFovByScreenSize();
        transferMapMgr = new NinTransferMapMgr(mapLoader);
        yarnMapMgr = new NinYarnMapMgr(mapLoader);
        yarnMgr = new NinYarnMgr(dialogueRunner);
        //cinemachineCamera?.UpdateFovByScreenSize();
    }
    public bool InventoryIsOpen
    {
        get
        {
            return MainMenuUI.Current.InventoryIsOpen;
        }
    }
    void Start()
    {

        // TestPrepareEnter();
        if(GameContext.Current != null && GameContext.Current.Trainer != null) {
            InitialMyTrainer(GameContext.Current.Trainer).Forget();
        }
    }
    void Update()
    {
        // if(isload) {
        //     isload = false;
        //     TestPrepareEnter();
        // }
        UploadMyLocAndUpdateAroundTrainer().Forget();
        lightManager.SetTime(weatherMgr.GetCurrentTimeHours());
    }
    public async UniTask<bool> StartBattleByNpcNameId(string npcNameId, Action<bool> complete) {
        // Find the NPC by name ID
        var npc = LocalNPC.GetNpcConfig(npcNameId);
        if (npc == null) {
            complete(false);
            return false;
        }

        var battleMatchAiMaker = new MainServer.BattleMatchAiMaker {
            AiType = MainServer.BattleMatchAiType.Npc
        };
        battleMatchAiMaker.NpcIds.Add(npcNameId);
        // Prepare the battle
        MainServer.BattlePrepare battlePrepare = new MainServer.BattlePrepare {
            BattleType = MainServer.BattleType.SingleNpcandDoublePokemon,
            BattleMatchAiMaker = battleMatchAiMaker
        };

        TryEnterBattle(battlePrepare, complete);
        return true;
    }
    public async UniTask<bool> StartBattleByPokeId(List<Int64> pokeIds, Action<bool> complete) {
        var battleMatchAiMaker = new MainServer.BattleMatchAiMaker {
            AiType = MainServer.BattleMatchAiType.SummonWild
        };
        battleMatchAiMaker.SummonPokeIds.Add(pokeIds);
        // Prepare the battle
        MainServer.BattlePrepare battlePrepare = new MainServer.BattlePrepare {
            BattleType = MainServer.BattleType.SingleWild,
            BattleMatchAiMaker = battleMatchAiMaker
        };

        TryEnterBattle(battlePrepare, complete);
        return true;
    }
    public void TryEnterBattle(MainServer.BattlePrepare battlePrepare, Action<bool> complete)
    {
        EnteringBattle();
        battleController.EnterBattleBy(BattleMapMaping.BattleBackgroupType.WildGrass, battlePrepare, (success) =>
        {
            if (success)
            {
                _createTs = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                EnteredBattle();
                // EnteredBattle();
            } else
            {
                EnteredBattle();
                ExitedBattle();
            }
            complete(success);
        });
    }
    public void EnterLocalBattle() {
        EnteringBattle();
    }
    public void EnteringBattle() {
        Debug.Log("正在载入战斗");
        mapCharacterMgr.FreezeMyCharacter();
        weatherMgr.PauseTime(true);
        MapTransitionAnimator.ToBattlePoint();
        // GameModule.UI.ShowUI<BattleLoadingWindow>();
        battleController.NewBattle();
        SetHiddenMapMenu(true);
    }
    public void EnteredBattle() {
        Debug.Log("载入战斗完成");
        TEngine.GameEvent.Send((int)GameEventType.EnterBattle);
        // GameModule.UI.CloseUI<BattleLoadingWindow>();
        // SetHiddenMapMenu(true);
    }
    public void ExitedBattle()
    {
        TEngine.GameEvent.Send((int)GameEventType.ExitBattle);
        // weatherMgr.PauseTime(false);
        mapCharacterMgr.UnFreezeMyCharacter();
        // weatherMgr.ResetWeather();
        mapCharacterMgr.myCharacter.Status = MainServer.TrainerActionType.Idle;
        Debug.Log($"=========: ExitBattle {mapCharacterMgr.myCharacter.Status}");
        SetHiddenMapMenu(false);
    }
    public void HandleCheckerResult(NinCharacter checkCharacter, CheckerResult checkerResult, Action<bool> complete)
    {
        if (checkerResult.tilePrefabComponent == null) {
            complete(false);
            return;
        }
        if(!string.IsNullOrEmpty(checkerResult.tilePrefabComponent.Bgm)) {
            SoundPlayer.PlaySound(checkerResult.tilePrefabComponent.Bgm);
        }
        if (checkerResult.tilePrefabComponent.IsBattle)
        {
            if (!mapCharacterMgr.myCharacter.trainer.Equals(checkCharacter.trainer))
            {
                complete(false);
            }
            var encounterMethod = mapCharacterMgr.myCharacter.GetIsOnWater() ? MainServer.EncounterMethod.Surf : MainServer.EncounterMethod.Walk;
            var ts = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            if (ts - _createTs < 5)
            {
                PLog.Info("不能频繁遇到敌人");
                complete(false);
                return;
            }
            MainServer.BattlePrepare battlePrepare = new MainServer.BattlePrepare
            {
                BattleType = MainServer.BattleType.SingleWild,
                BattleMatchAiMaker = new MainServer.BattleMatchAiMaker
                {
                    AiType = MainServer.BattleMatchAiType.NormalWild,
                    ReginId = checkerResult.tilePrefabComponent.RegionId,
                    AreaId = checkerResult.tilePrefabComponent.AreaId,
                    EncounterMethod = encounterMethod
                    // ReginId = "sinnoh",
                    // AreaId = "canalave-city-area"
                },
            };
            var oldStatus = checkCharacter.Status;
            checkCharacter.Status = MainServer.TrainerActionType.Battle;
            TryEnterBattle(battlePrepare, (success) =>
            {
                checkCharacter.Status = oldStatus;
                complete(success);
            });
        }
        else
        {
            complete(false);
        }
    }
    public void UpdateMyInfo(MainServer.Trainer trainer) {
        GameContext.Current.Trainer = trainer;
        mapCharacterMgr.myCharacter.SetTrainer(trainer);
        NotificationCenter.Default.PostNotification<MainServer.Trainer>(trainer);
    }
    public async UniTask InitialMyTrainer(MainServer.Trainer trainer)
    {
        // mapLoader.SetMapInfo(trainer.LocMapInfo());
        // await mapLoader.AsyncLoadMap(trainer.LocPosion());

        await mapCharacterMgr.InitialMyCharacter(trainer);
        var result = await Client.Share.GetTrainerAroundPokes();
        if (result.Success)
        {
            var mapUIWindow = await GameModule.UI.ShowUIAsyncAwait<MapUIWindow>();
            mapMenuUI = mapUIWindow.mainMenuUI;
            GameModule.UI.ShowUIAsync<DebugWindow>();
            mapMenuUI.SetTrainer(trainer);
            await UpdateFollowPokes(GameContext.Current.Trainer.FollowPoke.Pokes.ToArray(), GameContext.Current.Trainer.Id);
        }
    }

    public async UniTaskVoid TestPrepareEnter()
    {
         var account = await Account.Login(GameContext.Current.MessageHandler);
         if(account == null) {
            Debug.Log($"=========: 登录失败");
            // UIContext.Current.ToastError("登录失败");
            return;
         }
        // Debug.Log("Awake: async");
        // UnityMainThreadDispatcher.Instance().Enqueue(TestPrepareEnter);
        // TestPrepareEnter
        // isload = true;
        int tid = 1;
#if UNITY_EDITOR
        if (CoreContext.IsCopyProj())
        {
            tid = 3;
        }
#endif
        var result = await Client.Share.SelectTrainer(tid);
        if (result.Success)
        {
            GameContext.Current.Trainer = result.Content.Trainer;
            await InitialMyTrainer(GameContext.Current.Trainer);
            // await Client.Share.GetTrainerAroundPokes();
        }
        else
        {
            UIContext.Current.ToastError("导入训练师错误");
            UIContext.Current.ToastError(result.Error); // 输出错误信息
        }
        // StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.SelectTrainer(tid), result =>
        // {
        //     if (result.Success)
        //     {
        //         GameContext.Current.Trainer = result.Content.Trainer;
        //         InitialMyTrainer(GameContext.Current.Trainer);
        //         // NewBattle();
        //         try
        //         {
        //             StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.GetTrainerAroundPokes(), (result) =>
        //             {
        //                 Debug.Log($"=========: {result}");
        //                 // if(result.Success) {
        //                 //     NewBattle();
        //                 // }
        //                 // if(result.Success) {
        //                 //     var jsonResult = JsonFormatter.Default.Format(result.Result);
        //                 //     DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
        //                 // }
        //             }));
        //             // StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.CreateCustomMatch(), result => {
        //             //     if(result.Success) {
        //             //         GameContext.Current.CustomMatchId = result.Result;
        //             //         StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.GetTrainerAroundPokes(), (result) => {
        //             //                 Debug.Log($"=========: {result}");
        //             //                 // if(result.Success) {
        //             //                 //     NewBattle();
        //             //                 // }
        //             //                 // if(result.Success) {
        //             //                 //     var jsonResult = JsonFormatter.Default.Format(result.Result);
        //             //                 //     DefaultKeyValueStore.Instance.Set(GameConst.KVKey.Inventorys, jsonResult);
        //             //                 // }
        //             //         }));
        //             //     } else {
        //             //         UIContext.Current.ToastError("创建战斗信息错误");
        //             //         UIContext.Current.ToastError(result.Error);
        //             //     }
        //             // }));
        //         }
        //         catch
        //         {
        //             Debug.Log("错误");
        //         }
        //     }
        //     else
        //     {
        //         UIContext.Current.ToastError("导入训练师错误");
        //         UIContext.Current.ToastError(result.Error); // 输出错误信息
        //     }
        // }));
    }
    public async UniTask UpdateOtherTrainers(MainServer.Trainer[] trainers)
    {
        await this.mapCharacterMgr.UpdateOtherTrainers(trainers);
    }
    public async UniTask UpdateFollowPokes(MainServer.TrainerFollowPokeInfo[] trainerFollowPokes, Int64 trainerId)
    {
        await this.mapCharacterMgr.UpdateFollowPokes(trainerFollowPokes, trainerId);
    }
    public void SetHiddenMapMenu(bool hidden)
    {
        if(hidden) {
            // GameModule.UI.HideUI<MapUIWindow>();
            mapMenuUI.gameObject.SetActive(false);
        } else {
            mapMenuUI.gameObject.SetActive(true);
            // GameModule.UI.ShowUI<MapUIWindow>();
        }
        // mapMenuUI.SetHidden(hidden);
        // var alpha = hidden ? 0 : 0.5f;
        // if (GUIManager.Current.Buttons != null)
        // {
        //     GUIManager.Current.Buttons.alpha = alpha;
        // }
        // if (GUIManager.Current.Joystick != null)
        // {
        //     GUIManager.Current.Joystick.alpha = alpha;
        // }
    }
    public async UniTaskVoid UploadMyLocAndUpdateAroundTrainer()
    {
        if (mapCharacterMgr.myCharacter == null)
        {
            return;
        }
        if(GameContext.Current.Trainer == null) {
            return;
        }
        mapLoader.UpdatePostion(mapCharacterMgr.myCharacter.transform.position);
        long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        // var time = 500;
        var time = mapCharacterMgr.myCharacter.isMoving ? 300 : 1000;
        time = 1000;
        // if(_lastUpPosition == this.transform.position) {
        //     time = 300 * 10 * 10; // 30s
        // }
        if(mapCharacterMgr.myCharacter.isMoving && timestamp - _lastUpdateMapTs > 1000) {
            _lastUpdateMapTs = timestamp;
            mapLoader.LoadMap(mapCharacterMgr.myCharacter.transform.position);
        }
        if (timestamp - lastLocTs > time)
        {
            lastLocTs = timestamp;
            // Debug.Log($"=========:1 {mapCharacterMgr.myCharacter.transform.position}");
            // var characterPosition = mapCharacterMgr.myCharacter.transform.position;
            // var locInfo = new MainServer.TrainerLocInfo{
            //     Loc = new MainServer.TrainerLoc {
            //         X = characterPosition.x,
            //         Y = characterPosition.y,
            //         Z = characterPosition.z
            //     }
            // };
            var locInfo = mapCharacterMgr.myCharacter.ClearLocInfo();
            if(locInfo != null && locInfo.Loc != null) {
                locInfo.Loc.MainLandType = mapLoader.GetMainLandType();
                locInfo.Loc.MapName = mapLoader.GetMapInfo().GetMapNameId();
                var result = await Client.Share.UpdateUserLoc(locInfo);
                if (result.Success) {
                    await UpdateOtherTrainers(result.Content.Trainers.ToArray());
                }
            }
            // StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.UpdateUserLoc(mapCharacterMgr.myCharacter.ClearLocInfo()), (result) =>
            // {

            //     if (result.Success)
            //     {
            //         // _lastUpPosition = this.transform.position;
            //         // Debug.Log($"=========:2 {result.Result.Trainers.FirstOrDefault().Loc}");
            //         UpdateOtherTrainers(result.Content.Trainers.ToArray());
            //     }
            // }));
        }
        if (lastLocTs == 0)
        {
            lastLocTs = timestamp;
        }
    }
    public async UniTask<NinPlotChatacter> AddNinPlotChatacter(string pokeName) {
        var plotChatacter = mapCharacterMgr.CreateNinPlotChatacter(mapCharacterMgr.myCharacter.transform.position);
        // plotChatacter.SetLocalPositionEdge(mapCharacterMgr.myCharacter.transform.position, true);
        await plotChatacter.LoadPokeAnimation(pokeName);
        return plotChatacter;
    }
    public async UniTask MovePlotChatacter(NinPlotChatacter plotChatacter, Vector3 targetPosition, bool freeze = true, float duration = 1.5f) {
        //-25  //+10 // +10
        if(freeze) {
            mapCharacterMgr.FreezeMyCharacter();
        }
        await plotChatacter.startTrack(targetPosition, duration);
        if(freeze) {
            mapCharacterMgr.UnFreezeMyCharacter();
        }
    }
    // public async UniTask MoveToNewMap(string mapName) {
    //     // var mapName = mapName;
    //     var mapInfo = InstanceMapMgr.GetInstanceMapInfo(mapName);
    //     // var mapInfo = MapData.LoadMapData(mapName);
    //     if(mapInfo == null) {
    //         Debug.LogError("无法获取mapInfo");
    //         return ;
    //     }
    //     mapLoader.SetMapInfo(mapInfo);
    //     var initPosition = mapLoader.GetDefaultInitPosition();
    //     await UniTask.Yield();
    //     mapCharacterMgr.myCharacter.ninChatacterTransfer.Transfer(initPosition, (complete) => {
    //         if(complete) {
    //             mapLoader.AsyncLoadMap(initPosition).Forget();
    //             // mapLoader.LoadNearby(mapInfo.GetInitPosition());
    //         }
    //     });
    // }
    
    // public async UniTask MoveToNewMap(IMapInfo mapInfo) {
    //     MapTransitionAnimator.ToMapPoint();
    //     mapLoader.SetMapInfo(mapInfo);
    //     var initPosition = mapLoader.GetDefaultInitPosition();
    //     await UniTask.Yield();
    //     mapCharacterMgr.myCharacter.ninChatacterTransfer.Transfer(initPosition, (complete) => {
    //         if(complete) {
    //             mapLoader.AsyncLoadMap(initPosition).Forget();
    //             // mapLoader.LoadNearby(mapInfo.GetInitPosition());
    //         }
    //     });
    // }
    // void UpdateLoc() {
    //     long timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    //     var time = 300;
    //     if(_lastUpPosition == this.transform.position) {
    //         time = 300 * 10 * 10; // 30s
    //     }
    //     if(timestamp - lastLocTs > time) {
    //         Debug.Log($"=========:1 {this.transform.position}");
    //         StartCoroutine(Client.ExecuteRpcTaskCoroutine(Client.Share.UpdateUserLoc("test_map", this.transform.position.x, this.transform.position.y, this.transform.position.z), (result) => {

    //             if(result.Success) {
    //                 _lastUpPosition = this.transform.position;
    //                 // Debug.Log($"=========:2 {result.Result.Trainers.FirstOrDefault().Loc}");
    //                 MapController.Current.UpdateOtherTrainers(result.Result.Trainers.ToArray());
    //             }
    //         }));
    //         lastLocTs = timestamp;
    //     }
    //     if(lastLocTs == 0) {
    //         lastLocTs = timestamp;
    //     }
    // }
}